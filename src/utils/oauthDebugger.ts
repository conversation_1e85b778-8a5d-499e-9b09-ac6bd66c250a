// OAuth debugging utility to help verify redirect URLs
import { getAuthRedirectUrl, getOAuthCallbackUrl, isOAuthCallback, isProduction } from '@/config/auth';

export const debugOAuthConfiguration = () => {
  if (typeof window === 'undefined') {
    console.log('OAuth Debug: Running in server environment');
    return;
  }

  const currentUrl = window.location.href;
  const hostname = window.location.hostname;
  const port = window.location.port;
  const isProduction = window.location.hostname === 'dubgate.ca';
  
  console.group('🔐 OAuth Configuration Debug');
  console.log('Current URL:', currentUrl);
  console.log('Hostname:', hostname);
  console.log('Port:', port);
  console.log('Is Production:', isProduction);
  console.log('Is OAuth Callback:', isOAuthCallback());
  
  console.group('📍 Redirect URLs');
  console.log('Dashboard redirect:', getAuthRedirectUrl('/dashboard'));
  console.log('Default redirect:', getAuthRedirectUrl());
  console.log('Development callback URL:', getOAuthCallbackUrl('development'));
  console.log('Production callback URL:', getOAuthCallbackUrl('production'));
  console.groupEnd();
  
  console.group('🏗️ Google Console Configuration');
  console.log('Add these URLs to your Google Console "Authorized redirect URIs":');
  console.log('1. https://dubgate.ca/dashboard (Production)');
  console.log('2. http://localhost:8080/dashboard (Development)');
  console.groupEnd();
  
  console.groupEnd();
};

// Function to test OAuth redirect URL generation
export const testOAuthRedirectUrl = (path: string = '/dashboard') => {
  const redirectUrl = getAuthRedirectUrl(path);
  console.log(`🔗 OAuth Redirect URL for path "${path}": ${redirectUrl}`);
  return redirectUrl;
};

// Function to validate if current environment matches expected OAuth configuration
export const validateOAuthEnvironment = () => {
  const currentRedirectUrl = getAuthRedirectUrl('/dashboard');
  const expectedUrls = [
    'http://localhost:8080/dashboard',
    'https://dubgate.ca/dashboard'
  ];
  
  const isValid = expectedUrls.includes(currentRedirectUrl);
  
  console.log('🔍 OAuth Environment Validation');
  console.log('Current redirect URL:', currentRedirectUrl);
  console.log('Expected URLs:', expectedUrls);
  console.log('Is valid:', isValid ? '✅' : '❌');
  
  if (!isValid) {
    console.warn('⚠️  Your redirect URL does not match Google Console configuration!');
    console.warn('Make sure your Google Console has the following redirect URIs:');
    expectedUrls.forEach((url, index) => {
      console.warn(`   ${index + 1}. ${url}`);
    });
  }
  
  return isValid;
};