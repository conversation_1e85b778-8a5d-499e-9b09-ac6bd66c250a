# OAuth Configuration Fix

## Problem
The Google OAuth redirect URI mismatch occurs when the redirect URI in your code doesn't match the ones configured in Google Console.

## Root Cause
The original code was using `window.location.origin` which would generate different protocols:
- Development: `http://localhost:8080/dashboard`
- Production: `https://dubgate.ca/dashboard`

But Google Console was only configured with `https://` URLs.

## Solution Applied

### 1. Fixed `getAuthRedirectUrl` function
The function now explicitly handles protocol based on environment:
- **Development**: Always uses `http://localhost:8080/dashboard`
- **Production**: Always uses `https://dubgate.ca/dashboard`

### 2. Google Console Configuration
You need to add these **exact** URLs to your Google Console "Authorized redirect URIs":

```
1. https://dubgate.ca/dashboard
2. http://localhost:8080/dashboard
```

### 3. Steps to Fix Google Console

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to **APIs & Services** → **Credentials**
3. Click on your OAuth 2.0 Client ID
4. In the "Authorized redirect URIs" section, add:
   - `https://dubgate.ca/dashboard`
   - `http://localhost:8080/dashboard`
5. Remove any old/incorrect URIs
6. Save the configuration

### 4. Testing the Fix

1. **Development Testing**:
   - Start your dev server: `npm run dev`
   - Go to `http://localhost:8080/signin`
   - Click "Continue with Google"
   - Check browser console for OAuth debug info

2. **Production Testing**:
   - Deploy to `https://dubgate.ca`
   - Go to `https://dubgate.ca/signin`
   - Click "Continue with Google"
   - Check browser console for OAuth debug info

### 5. Debug Information

The code now includes debug logging that will show:
- Current environment (development/production)
- Generated redirect URL
- Whether it matches expected configuration
- OAuth callback detection

Check the browser console when clicking "Continue with Google" to see detailed debug information.

### 6. Environment Detection

The system now properly detects:
- **Development**: `localhost` or `127.0.0.1`
- **Production**: `dubgate.ca` or `www.dubgate.ca`

### 7. Common Issues

**Issue**: Still getting "redirect_uri_mismatch" error
**Solution**: 
1. Clear browser cache
2. Verify Google Console has exact URLs listed above
3. Check browser console for debug info
4. Ensure no typos in Google Console URIs

**Issue**: Works in development but not production
**Solution**:
1. Verify `https://dubgate.ca/dashboard` is in Google Console
2. Check if your domain is properly configured with HTTPS
3. Ensure SSL certificate is valid

**Issue**: OAuth callback not working
**Solution**:
1. Verify `/dashboard` route is properly configured
2. Check that `RequireAuth` wrapper is working
3. Ensure Supabase is handling the OAuth callback

## Files Modified

1. `src/config/auth.ts` - Fixed redirect URL generation
2. `src/pages/SignIn.tsx` - Added debug logging
3. `src/pages/SignUp.tsx` - Added debug logging
4. `src/utils/oauthDebugger.ts` - New debugging utility

## Next Steps

1. Update your Google Console with the exact URLs above
2. Test OAuth flow in both development and production
3. Monitor browser console for any remaining issues
4. Remove debug logging once everything is working (optional)